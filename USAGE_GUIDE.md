# Flyright Database Viewer - Usage Guide

## Quick Start

1. **Run the application**:
   ```bash
   python3 db_viewer.py
   ```

2. **Browse the main database**: The application loads all 121 records from your FlyrightDriveDatabase.db

3. **View related data**: Double-click any row to see detailed information plus related simulator database records

## Main Features

### 🔍 **Search and Filter**
- **Real-time search**: Type in the search box to filter across all fields
- **Column sorting**: Click any column header to sort by that column
- **Filter menus**: Use View menu to filter by Computer type or Simulator number

### 📊 **Record Details with Related Data**
When you double-click on any row, you'll see:

#### **Main Record Tab**
- All details from the clicked drive record
- Computer, Simulator, Asset Tag, Serial Number, etc.

#### **Related Database Tabs**
- Automatically loads data from `/home/<USER>/Desktop/DriveDatabase/simulator_databases/{SIMULATOR}/{COMPUTER}.db`
- Shows log entries with dates, actions, and comments
- **Log Comment column is positioned second from left** for easy reading
- Each tab displays the full table with sortable columns

### 🎯 **Smart Database Matching**

The application uses intelligent matching to find related databases:

1. **Exact Match**: Looks for `{Computer}.db` (e.g., `Avionics_Software.db`)
2. **Partial Match**: Searches for `*{Computer}*.db` if exact match not found
3. **Show All**: Displays all databases for that simulator if no matches found

### 📋 **Example Workflow**

1. **Click on "Avionics Software" record** (Simulator 1477)
2. **Application opens tabbed window** showing:
   - **Main Record**: Drive details
   - **Avionics_Software**: Log entries from that specific database
   - **Other related databases**: Any other matching databases for simulator 1477

3. **Each related tab shows**:
   - **Column order optimized for readability**: Letter/Prisec, **Log Comment**, Asset Tag, Serial Number, Revision Number, Date, Action
   - Log entries with Asset Tags, Serial Numbers, Dates
   - Actions performed (Initial, Updates, etc.)
   - Comments and revision numbers (Log Comment column is wider for better text display)
   - Full scrollable table with all records

## Error Handling

✅ **Fixed Issues**:
- Window grab errors when opening detail windows
- Proper error handling for missing databases
- Graceful handling of database connection issues

✅ **Robust Features**:
- Shows error tabs if databases can't be read
- Continues working even if some related databases are missing
- Clear error messages for any issues

## Tips

- **Window Management**: Detail windows are centered and properly focused
- **Multiple Records**: You can open multiple detail windows simultaneously
- **Data Refresh**: Use File → Refresh to reload data if databases are updated
- **Database Selection**: Use File → Open Database to view different database files

## Troubleshooting

**If double-click doesn't work**:
- Make sure you're clicking on a data row, not the header
- Check that the database file exists and is readable

**If related data doesn't appear**:
- Verify the simulator database directory exists
- Check that database files are in the expected location
- Look for error messages in the related data tabs

**If the application won't start**:
- Ensure Python 3 and tkinter are installed
- Check that the main database file path is correct
- Run `python3 test_fix.py` to verify everything is working
