#!/usr/bin/env python3
"""
SQLite Database Viewer GUI Application
Displays data from FlyrightDriveDatabase.db in a user-friendly interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os

class DatabaseViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("Flyright Drive Database Viewer")
        self.root.geometry("1200x700")
        
        # Database path
        self.db_path = "/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"
        
        # Create menu bar
        self.create_menu()
        
        # Create main interface
        self.create_widgets()
        
        # Load data on startup
        self.load_data()
    
    def create_menu(self):
        """Create the menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open Database...", command=self.open_database)
        file_menu.add_command(label="Refresh", command=self.load_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Show All Records", command=self.show_all_records)
        view_menu.add_command(label="Filter by Computer", command=self.filter_by_computer)
        view_menu.add_command(label="Filter by Simulator", command=self.filter_by_simulator)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_widgets(self):
        """Create the main interface widgets"""
        # Status frame
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Database:").pack(side=tk.LEFT)
        self.db_label = ttk.Label(status_frame, text=self.db_path, foreground="blue")
        self.db_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # Search frame
        search_frame = ttk.Frame(self.root)
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Button(search_frame, text="Clear", command=self.clear_search).pack(side=tk.LEFT, padx=(5, 0))
        
        # Record count label
        self.count_label = ttk.Label(search_frame, text="")
        self.count_label.pack(side=tk.RIGHT)
        
        # Treeview frame
        tree_frame = ttk.Frame(self.root)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview with scrollbars
        self.tree = ttk.Treeview(tree_frame)
        
        # Vertical scrollbar
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # Horizontal scrollbar
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Configure columns
        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number", 
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]
        
        self.tree["columns"] = columns
        self.tree["show"] = "headings"
        
        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.tree.column(col, width=120, minwidth=80)
        
        # Bind double-click event
        self.tree.bind("<Double-1>", self.on_item_double_click)
    
    def load_data(self):
        """Load data from the database"""
        try:
            if not os.path.exists(self.db_path):
                messagebox.showerror("Error", f"Database file not found: {self.db_path}")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM Drives ORDER BY Computer, [Asset Tag]")
            self.all_data = cursor.fetchall()
            
            conn.close()
            
            self.display_data(self.all_data)
            
        except sqlite3.Error as e:
            messagebox.showerror("Database Error", f"Error reading database: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {e}")
    
    def display_data(self, data):
        """Display data in the treeview"""
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Insert new data
        for row in data:
            self.tree.insert("", tk.END, values=row)
        
        # Update record count
        self.count_label.config(text=f"Records: {len(data)}")
    
    def on_search(self, *args):
        """Handle search functionality"""
        search_term = self.search_var.get().lower()
        
        if not search_term:
            self.display_data(self.all_data)
            return
        
        # Filter data based on search term
        filtered_data = []
        for row in self.all_data:
            # Search in all columns
            if any(search_term in str(cell).lower() for cell in row):
                filtered_data.append(row)
        
        self.display_data(filtered_data)
    
    def clear_search(self):
        """Clear the search field"""
        self.search_var.set("")
    
    def sort_by_column(self, column):
        """Sort data by the specified column"""
        # Get column index
        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number", 
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]
        col_index = columns.index(column)
        
        # Get current data
        current_data = []
        for item in self.tree.get_children():
            current_data.append(self.tree.item(item)["values"])
        
        # Sort data
        try:
            # Try numeric sort for numeric columns
            if column in ["Simulator", "Revision Number", "UUID"]:
                current_data.sort(key=lambda x: int(x[col_index]) if x[col_index] else 0)
            else:
                current_data.sort(key=lambda x: str(x[col_index]).lower())
        except (ValueError, TypeError):
            # Fall back to string sort
            current_data.sort(key=lambda x: str(x[col_index]).lower())
        
        self.display_data(current_data)
    
    def on_item_double_click(self, event):
        """Handle double-click on tree item"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item["values"]
            self.show_record_details_with_related(values)
    
    def show_record_details(self, values):
        """Show detailed view of a record"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title("Record Details")
        detail_window.geometry("400x300")
        detail_window.transient(self.root)
        detail_window.grab_set()

        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number",
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]

        # Create labels for each field
        for i, (col, val) in enumerate(zip(columns, values)):
            frame = ttk.Frame(detail_window)
            frame.pack(fill=tk.X, padx=10, pady=2)

            ttk.Label(frame, text=f"{col}:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
            ttk.Label(frame, text=str(val), wraplength=250).pack(side=tk.LEFT, padx=(10, 0))

        # Close button
        ttk.Button(detail_window, text="Close", command=detail_window.destroy).pack(pady=10)

    def show_record_details_with_related(self, values):
        """Show detailed view of a record with related database records"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title("Record Details with Related Data")
        detail_window.geometry("1000x700")
        detail_window.transient(self.root)
        detail_window.grab_set()

        # Create notebook for tabs
        notebook = ttk.Notebook(detail_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Main record tab
        main_tab = ttk.Frame(notebook)
        notebook.add(main_tab, text="Main Record")

        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number",
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]

        # Create labels for each field in main tab
        for i, (col, val) in enumerate(zip(columns, values)):
            frame = ttk.Frame(main_tab)
            frame.pack(fill=tk.X, padx=10, pady=2)

            ttk.Label(frame, text=f"{col}:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
            ttk.Label(frame, text=str(val), wraplength=250).pack(side=tk.LEFT, padx=(10, 0))

        # Get computer and simulator values for related data
        computer = str(values[0]).replace(" ", "_")  # Replace spaces with underscores
        simulator = str(values[1])

        # Load related data from simulator databases
        self.load_related_data(notebook, computer, simulator)

        # Close button
        button_frame = ttk.Frame(detail_window)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="Close", command=detail_window.destroy).pack()

    def load_related_data(self, notebook, computer, simulator):
        """Load related data from simulator databases"""
        import glob

        # Base path for simulator databases
        base_path = f"/home/<USER>/Desktop/DriveDatabase/simulator_databases/{simulator}"

        if not os.path.exists(base_path):
            # Create a tab to show no data found
            no_data_tab = ttk.Frame(notebook)
            notebook.add(no_data_tab, text="No Related Data")
            ttk.Label(no_data_tab, text=f"No simulator database found for simulator {simulator}").pack(pady=20)
            return

        # Find database files that match the computer name
        db_pattern = os.path.join(base_path, f"{computer}.db")
        db_files = glob.glob(db_pattern)

        # Also try with wildcards for partial matches
        if not db_files:
            db_pattern = os.path.join(base_path, f"*{computer}*.db")
            db_files = glob.glob(db_pattern)

        if not db_files:
            # Show all available databases for this simulator
            all_dbs = glob.glob(os.path.join(base_path, "*.db"))
            if all_dbs:
                self.show_all_simulator_databases(notebook, all_dbs, simulator)
            else:
                no_data_tab = ttk.Frame(notebook)
                notebook.add(no_data_tab, text="No Related Data")
                ttk.Label(no_data_tab, text=f"No databases found for simulator {simulator}").pack(pady=20)
            return

        # Load data from each matching database
        for db_file in db_files:
            db_name = os.path.basename(db_file).replace('.db', '')
            self.create_related_data_tab(notebook, db_file, db_name)

    def show_all_simulator_databases(self, notebook, db_files, simulator):
        """Show all available databases for a simulator when no exact match is found"""
        for db_file in db_files:
            db_name = os.path.basename(db_file).replace('.db', '')
            self.create_related_data_tab(notebook, db_file, f"{db_name} (Sim {simulator})")

    def create_related_data_tab(self, notebook, db_file, tab_name):
        """Create a tab showing data from a related database"""
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            if not tables:
                conn.close()
                return

            # For now, assume the main table is 'logs' or use the first table
            table_name = 'logs'
            if ('logs',) not in tables:
                table_name = tables[0][0]

            # Get all data from the table
            cursor.execute(f"SELECT * FROM {table_name}")
            data = cursor.fetchall()

            # Get column names
            cursor.execute(f"PRAGMA table_info({table_name})")
            column_info = cursor.fetchall()
            column_names = [col[1] for col in column_info]

            conn.close()

            # Create tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=tab_name)

            # Create treeview for the data
            tree_frame = ttk.Frame(tab)
            tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            tree = ttk.Treeview(tree_frame)
            tree["columns"] = column_names
            tree["show"] = "headings"

            # Configure columns
            for col in column_names:
                tree.heading(col, text=col)
                tree.column(col, width=120, minwidth=80)

            # Add scrollbars
            v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
            v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            tree.configure(yscrollcommand=v_scrollbar.set)

            h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
            h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
            tree.configure(xscrollcommand=h_scrollbar.set)

            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # Insert data
            for row in data:
                tree.insert("", tk.END, values=row)

            # Add record count label
            count_label = ttk.Label(tab, text=f"Records: {len(data)}")
            count_label.pack(pady=5)

        except sqlite3.Error as e:
            # Create error tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=f"Error: {tab_name}")
            ttk.Label(tab, text=f"Error loading database: {e}").pack(pady=20)
        except Exception as e:
            # Create error tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=f"Error: {tab_name}")
            ttk.Label(tab, text=f"Unexpected error: {e}").pack(pady=20)
    
    def open_database(self):
        """Open a different database file"""
        filename = filedialog.askopenfilename(
            title="Select Database File",
            filetypes=[("SQLite files", "*.db"), ("All files", "*.*")]
        )
        
        if filename:
            self.db_path = filename
            self.db_label.config(text=self.db_path)
            self.load_data()
    
    def show_all_records(self):
        """Show all records"""
        self.clear_search()
        self.display_data(self.all_data)
    
    def filter_by_computer(self):
        """Filter records by computer type"""
        # Get unique computer types
        computers = list(set(row[0] for row in self.all_data))
        computers.sort()
        
        # Create selection dialog
        self.create_filter_dialog("Filter by Computer", computers, 0)
    
    def filter_by_simulator(self):
        """Filter records by simulator number"""
        # Get unique simulator numbers
        simulators = list(set(str(row[1]) for row in self.all_data))
        simulators.sort()
        
        # Create selection dialog
        self.create_filter_dialog("Filter by Simulator", simulators, 1)
    
    def create_filter_dialog(self, title, options, column_index):
        """Create a filter selection dialog"""
        filter_window = tk.Toplevel(self.root)
        filter_window.title(title)
        filter_window.geometry("300x400")
        filter_window.transient(self.root)
        filter_window.grab_set()
        
        ttk.Label(filter_window, text="Select value to filter by:").pack(pady=10)
        
        # Listbox with options
        listbox = tk.Listbox(filter_window)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        for option in options:
            listbox.insert(tk.END, option)
        
        def apply_filter():
            selection = listbox.curselection()
            if selection:
                selected_value = listbox.get(selection[0])
                filtered_data = [row for row in self.all_data if str(row[column_index]) == selected_value]
                self.display_data(filtered_data)
                filter_window.destroy()
        
        # Buttons
        button_frame = ttk.Frame(filter_window)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="Apply", command=apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=filter_window.destroy).pack(side=tk.LEFT, padx=5)
    
    def show_about(self):
        """Show about dialog"""
        messagebox.showinfo("About", 
                          "Flyright Drive Database Viewer\n\n"
                          "A GUI application for viewing SQLite database records.\n"
                          "Double-click on any record for detailed view.\n\n"
                          "Features:\n"
                          "• Search across all fields\n"
                          "• Sort by column headers\n"
                          "• Filter by computer or simulator\n"
                          "• Detailed record view")

def main():
    root = tk.Tk()
    app = DatabaseViewer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
