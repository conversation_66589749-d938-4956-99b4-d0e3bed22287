# Flyright Database Viewer

A GUI application for viewing and browsing SQLite database records from the Flyright Drive Database.

## Features

- **Menu-driven interface** with File, View, and Help menus
- **Search functionality** across all database fields
- **Sortable columns** - click on any column header to sort
- **Filter options** by Computer type or Simulator number
- **Split-pane interface** - main database list on the left, detailed record view on the right
- **Integrated detail view** - double-click any record to see full details plus related simulator database records in the right panel
- **Database file selection** - open different database files
- **Real-time search** as you type

## Installation

1. Run the installation script:
   ```bash
   ./install.sh
   ```

2. The application will be installed and added to your application menu under the Office or Development category.

## Usage

### Running the Application

- **From the application menu**: Look for "Flyright Database Viewer"
- **From command line**: `python3 db_viewer.py`

### Using the Interface

1. **Main View**: The application displays all database records in a table format
2. **Search**: Use the search box to filter records across all fields
3. **Sort**: Click on any column header to sort by that column
4. **Filter**: Use the View menu to filter by specific criteria
5. **Details**: Double-click any record to see detailed information and related simulator database records in the right panel
6. **Refresh**: Use File → Refresh to reload data from the database

### Menu Options

#### File Menu
- **Open Database**: Select a different SQLite database file
- **Refresh**: Reload data from the current database
- **Exit**: Close the application

#### View Menu
- **Show All Records**: Clear all filters and show complete dataset
- **Filter by Computer**: Show only records for a specific computer type
- **Filter by Simulator**: Show only records for a specific simulator number

#### Help Menu
- **About**: Information about the application

## Database Structure

The application expects a SQLite database with a "Drives" table containing these columns:
- Computer
- Simulator
- Asset Tag
- Serial Number
- Letter/PriSec
- Revision Number
- Sibling Drives
- Update Needed
- UUID

### Related Simulator Databases

When you double-click on a record, the application will also look for related databases in:
`/home/<USER>/Desktop/DriveDatabase/simulator_databases/{SIMULATOR}/{COMPUTER}.db`

Where:
- `{SIMULATOR}` is the simulator number from the clicked record
- `{COMPUTER}` is the computer name from the clicked record (with spaces replaced by underscores)

These related databases typically contain a "logs" table with columns:
- Letter/Prisec
- Asset Tag
- Serial Number
- Revision Number
- Date
- Action
- Log Comment

If no exact match is found, the application will show all available databases for that simulator.

## MenuLibre Integration

After installation, you can customize the menu entry using MenuLibre:

1. Open MenuLibre
2. Navigate to Office or Development category
3. Find "Flyright Database Viewer"
4. Edit properties as needed (icon, category, etc.)

## Requirements

- Python 3.x
- tkinter (usually included with Python)
- SQLite3 (included with Python)

## Troubleshooting

- **Database not found**: Check that the database path is correct in the application
- **Permission errors**: Ensure you have read access to the database file
- **Application doesn't appear in menu**: Try running `update-desktop-database ~/.local/share/applications/`
